name: GitHub-Profile-3D-Contrib

on:
  schedule: # 03:00 JST == 18:00 UTC
    - cron: "0 18 * * *"
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    name: generate-github-profile-3d-contrib
    steps:
      - uses: actions/checkout@v3
      - uses: yoshi389111/github-profile-3d-contrib@v2.0.1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          USERNAME: vickyymosafan
        with:
          USERNAME: vickyymosafan
          MAX_REPOS: 100
      - name: Commit & Push
        run: |
          git config user.name github-actions
          git config user.email <EMAIL>
          git add .
          git commit -m "generated"
          git push
