<!DOCTYPE html>
<html>
<head>
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 60px 0;
            text-align: center;
            border-radius: 20px;
            margin-bottom: 40px;
        }
        .hero-content {
            color: white;
        }
        .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        .card {
            background: #f8fafc;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
        }
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }
        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
    </style>
</head>
<body>

<div class="hero-section">
    <div class="hero-content">
        <h1>🚀 M<PERSON></h1>
        <h2>Fullstack Developer & UI/UX Enthusiast</h2>

        <!-- Typing Animation -->
        <img src="https://readme-typing-svg.demolab.com?font=Fira+Code&size=24&duration=3000&pause=1000&color=FFFFFF&center=true&vCenter=true&width=700&lines=Selamat+Datang+di+Profil+Saya!;Fullstack+Developer+Berpengalaman;UI/UX+Enthusiast+%26+Problem+Solver;Membangun+Solusi+Digital+End-to-End;Mari+Berkolaborasi+Bersama!" alt="Typing SVG" />

        <!-- Profile Views -->
        <div style="margin-top: 20px;">
            <img src="https://komarev.com/ghpvc/?username=vickyymosafan&label=Pengunjung%20Profil&color=white&style=flat-square" alt="Profile Views" />
        </div>
    </div>
</div>

<div class="grid-container">
    <div class="card">
        <h2>👨‍💻 Tentang Saya</h2>
        <div style="text-align: left;">
            <pre><code>const vicky = {
    nama: "M. Vicky Mosafan",
    lokasi: "Surabaya, Jawa Timur, Indonesia",
    pendidikan: "Universitas Muhammadiyah Jember",
    peran: "Fullstack Developer & UI/UX Enthusiast",
    fokus: "End-to-end digital solutions",
    sedangBelajar: ["Next.js", "TypeScript", "React Native", "Figma"],
    keahlian: ["Frontend", "Backend", "UI/UX Design"],
    motto: "Design with purpose, code with passion! 🎨💻"
};</code></pre>
        </div>
    </div>

    <div class="card">
        <h2>🎯 Saat Ini Fokus Pada</h2>
        <ul style="text-align: left; list-style: none; padding: 0;">
            <li>🔭 Mengembangkan aplikasi fullstack dengan <strong>React & Node.js</strong></li>
            <li>🎨 Merancang pengalaman pengguna yang intuitif dengan <strong>Figma</strong></li>
            <li>🌱 Mempelajari <strong>TypeScript</strong> dan <strong>React Native</strong></li>
            <li>👯 Mencari kolaborasi dalam proyek <strong>Open Source</strong></li>
            <li>💬 Siap membantu dengan <strong>Fullstack Development & UI/UX</strong></li>
            <li>⚡ <strong>Fun fact</strong>: Saya suka kopi dan design di malam hari! ☕🌙</li>
        </ul>
    </div>
</div>



<h2 style="text-align: center; margin: 50px 0 30px 0;">🛠️ Tech Stack & Keahlian</h2>

<div class="tech-grid">
    <div class="card">
        <h3>💻 Frontend Development</h3>
        <img src="https://skillicons.dev/icons?i=js,ts,react,nextjs,vue" style="margin: 10px 0;" />
        <img src="https://skillicons.dev/icons?i=html,css,tailwind,bootstrap,sass" style="margin: 10px 0;" />
    </div>

    <div class="card">
        <h3>⚙️ Backend Development</h3>
        <img src="https://skillicons.dev/icons?i=nodejs,express,python,php,java" style="margin: 10px 0;" />
        <img src="https://skillicons.dev/icons?i=mongodb,postgres,mysql,firebase,supabase" style="margin: 10px 0;" />
    </div>

    <div class="card">
        <h3>🎨 UI/UX Design</h3>
        <img src="https://skillicons.dev/icons?i=figma,photoshop,illustrator,xd" style="margin: 10px 0;" />
        <p style="margin-top: 15px; color: #64748b;">User Research • Wireframing • Prototyping • Design Systems</p>
    </div>

    <div class="card">
        <h3>🔧 Tools & Technologies</h3>
        <img src="https://skillicons.dev/icons?i=git,github,vscode,docker,vercel" style="margin: 10px 0;" />
        <img src="https://skillicons.dev/icons?i=netlify,heroku,aws,gcp" style="margin: 10px 0;" />
    </div>
</div>

<div style="text-align: center; margin: 50px 0;">
    <h2>🐍 Kontribusi GitHub</h2>
    <img src="https://raw.githubusercontent.com/vickyymosafan/vickyymosafan/output/github-contribution-grid-snake-dark.svg" alt="Snake Animation" style="max-width: 100%; border-radius: 10px;" />
</div>

<h2 style="text-align: center; margin: 50px 0 30px 0;">🤝 Mari Terhubung!</h2>

<div class="contact-grid">
    <div class="card" style="text-align: center;">
        <h3>📧 Email</h3>
        <a href="mailto:<EMAIL>" style="text-decoration: none;">
            <img src="https://img.shields.io/badge/<EMAIL>-D14836?style=for-the-badge&logo=gmail&logoColor=white" alt="Email" style="margin: 10px 0;"/>
        </a>
        <p style="color: #64748b; margin-top: 10px;">Untuk kolaborasi & diskusi proyek</p>
    </div>

    <div class="card" style="text-align: center;">
        <h3>💼 LinkedIn</h3>
        <a href="https://linkedin.com/in/vickymosafan/" target="_blank" style="text-decoration: none;">
            <img src="https://img.shields.io/badge/Vicky_Mosafan-0077B5?style=for-the-badge&logo=linkedin&logoColor=white" alt="LinkedIn" style="margin: 10px 0;"/>
        </a>
        <p style="color: #64748b; margin-top: 10px;">Networking profesional & karir</p>
    </div>

    <div class="card" style="text-align: center;">
        <h3>📸 Instagram</h3>
        <a href="https://instagram.com/frontendenthusiast" target="_blank" style="text-decoration: none;">
            <img src="https://img.shields.io/badge/@frontendenthusiast-E4405F?style=for-the-badge&logo=instagram&logoColor=white" alt="Instagram" style="margin: 10px 0;"/>
        </a>
        <p style="color: #64748b; margin-top: 10px;">Behind the scenes & inspirasi</p>
    </div>
</div>

<h2 style="text-align: center; margin: 50px 0 30px 0;">🎯 Proyek Unggulan</h2>

<div class="grid-container">
    <div class="card">
        <h3>🌟 Portfolio Website</h3>
        <p style="color: #64748b; margin: 15px 0;">Website portfolio personal dengan design modern dan interaktif menggunakan React & Next.js</p>
        <div style="margin: 15px 0;">
            <span style="background: #e2e8f0; padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">React</span>
            <span style="background: #e2e8f0; padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">Next.js</span>
            <span style="background: #e2e8f0; padding: 4px 8px; border-radius: 12px; font-size: 12px;">Tailwind CSS</span>
        </div>
    </div>

    <div class="card">
        <h3>📊 Dashboard Analytics</h3>
        <p style="color: #64748b; margin: 15px 0;">Dashboard analytics dengan visualisasi data real-time dan interface yang user-friendly</p>
        <div style="margin: 15px 0;">
            <span style="background: #e2e8f0; padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">Vue.js</span>
            <span style="background: #e2e8f0; padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">Node.js</span>
            <span style="background: #e2e8f0; padding: 4px 8px; border-radius: 12px; font-size: 12px;">MongoDB</span>
        </div>
    </div>

    <div class="card">
        <h3>🎨 Design System</h3>
        <p style="color: #64748b; margin: 15px 0;">Komprehensif design system dengan komponen reusable untuk konsistensi UI/UX</p>
        <div style="margin: 15px 0;">
            <span style="background: #e2e8f0; padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">Figma</span>
            <span style="background: #e2e8f0; padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">Storybook</span>
            <span style="background: #e2e8f0; padding: 4px 8px; border-radius: 12px; font-size: 12px;">React</span>
        </div>
    </div>
</div>

<div style="text-align: center; margin: 60px 0 40px 0; padding: 40px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 20px; color: white;">
    <h2 style="margin-bottom: 20px;">💫 Terima kasih telah mengunjungi profil saya!</h2>
    <p style="font-size: 18px; margin-bottom: 0;">Mari berkolaborasi dan menciptakan sesuatu yang luar biasa bersama! 🚀</p>
</div>

</body>
</html>
